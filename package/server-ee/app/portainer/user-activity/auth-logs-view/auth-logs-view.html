<page-header
  title="'User authentication logs'"
  breadcrumbs="['User authentication logs']"
  reload="true"
>
</page-header>

<div class="row">
  <div class="col-sm-12">
    <rd-widget>
      <rd-widget-body>
        <div class="form-horizontal">
          <div class="form-group">
            <label for="dateRangeInput" class="col-sm-2 control-label text-left">{{ 'userActivity.authLogs.dateRange' | translate: 'Date range' }}</label>
            <div class="col-sm-6">
              <date-range-picker
                id="dateRangeInput"
                class-name="form-control"
                start-date="$ctrl.state.date.from"
                end-date="$ctrl.state.date.to"
                max-date="$ctrl.today"
                min-date="$ctrl.minValidDate"
                on-change="($ctrl.onChangeDate)"
              ></date-range-picker>
            </div>
          </div>
        </div>

        <p class="text-muted small vertical-center">
          <pr-icon icon="'info'" class-name="'icon icon-sm icon-primary'"></pr-icon>
          {{ 'userActivity.authLogs.retentionInfo' | translate: 'Portainer user authentication logs have a maximum retention of 7 days.' }}
        </p>

        <div>
          <button type="button" class="btn btn-sm btn-primary mt-2" ng-click="$ctrl.export()"
            ><pr-icon icon="'download'" icon-class="'icon icon-sm'"></pr-icon>{{ 'userActivity.authLogs.exportCSV' | translate: 'Export as CSV' }}
          </button>
          <be-feature-indicator feature="$ctrl.limitedFeature"></be-feature-indicator>
        </div>
      </rd-widget-body>
    </rd-widget>
  </div>
</div>

<authentication-logs-table
  dataset="$ctrl.state.logs"
  keyword="$ctrl.state.keyword"
  sort="$ctrl.state.sort"
  limit="$ctrl.state.limit"
  context-filter="$ctrl.state.contextFilter"
  type-filter="$ctrl.state.typeFilter"
  total-items="$ctrl.state.totalItems"
  current-page="$ctrl.state.currentPage"
  feature="{{:: $ctrl.limitedFeature}}"
  on-change-context-filter="($ctrl.onChangeContextFilter)"
  on-change-type-filter="($ctrl.onChangeTypeFilter)"
  on-change-keyword="($ctrl.onChangeKeyword)"
  on-change-sort="($ctrl.onChangeSort)"
  on-change-limit="($ctrl.onChangeLimit)"
  on-change-page="($ctrl.onChangePage)"
></authentication-logs-table>
