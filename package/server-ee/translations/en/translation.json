{"Actions": "Actions", "Browse": "Browse", "Change window settings": "Change window settings", "Cluster status": "Cluster status", "Container Name": "Container Name", "Created": "Created", "Docker API version": "Docker API version", "Docker security settings": "Docker security settings", "Driver": "Driver", "Go to cluster visualizer": "Go to cluster visualizer", "Host and filesystem": "Host and filesystem", "Labels": "Labels", "Mount path": "Mount path", "Mounted At": "Mounted At", "Nodes": "Nodes", "Note: The recreate/duplicate/edit feature is currently hidden (for non-admin users) by one or more security settings": {"": "Note: The recreate/duplicate/edit feature is currently hidden (for non-admin users) by one or more security settings."}, "Other": "Other", "Read-only": "Read-only", "Remove this volume": "Remove this volume", "Save configuration": "Save configuration", "Saving": {"": {"": {"": "Saving..."}}}, "Secrets": "Secrets", "The environment must be": "The environment must be", "Total CPU": "Total CPU", "Total memory": "Total memory", "account": {"access-tokens": {"add-token": "Add access token", "adding-token": "Adding access token..."}, "application_settings": {"title": "Application settings"}, "change-password": {"confirm-password": "Confirm password", "current-password": "Current password", "here": "here.", "ldap-auth-warning": "You cannot change your password when using LDAP authentication.", "min-password-length": "Minimum password length is set", "new-password": "New password", "oauth-auth-warning": "You cannot change your password when using OAuth authentication.", "remind-later": "Remind me later", "title": "Change user password", "update-password": "Update password"}, "language-settings": {"error-title": "Error", "interface-language": "Interface language", "placeholder": "Select language...", "success-title": "Success", "tooltip": "Select your preferred language for the Portainer interface. Changes take effect immediately.", "update-success": "Language preference updated successfully"}, "user-settings": "User settings"}, "auth": {"authentication-in-progress": "Authentication in progress...", "confirm-password": "Confirm password", "current-password": "Current password", "error": {"invalid_oauth": "Invalid OAuth state, try again.", "login": "Unable to login", "node_limit": "Node limit exceeds the 5 node free license, please contact your administrator", "oauth_login": "Unable to login via OAuth", "retrieve_environments": "Unable to retrieve environments", "retrieve_license": "Unable to retrieve licenses info", "retrieve_public_settings": "Unable to retrieve public settings", "verify_admin": "Unable to verify administrator account existence"}, "login": "<PERSON><PERSON>", "login-in-progress": "Login in progress...", "login-title": "Log in to your account", "login-with-github": "Login with GitHub", "login-with-google": "Login with Google", "login-with-microsoft": "Login with Microsoft", "login-with-oauth": "Login with OAuth", "new-password": "New password", "password": "Password", "password-placeholder": "Enter your password", "update-password": "Update password", "use-internal-auth": "Use internal authentication", "username": "Username", "username-placeholder": "Enter your username", "welcome-message": "Welcome back! Please enter your details"}, "azure": {"container-instances": {"container-actions": {"confirm-delete": "Are you sure you want to delete the selected containers?", "restart": "<PERSON><PERSON>", "restarting": "Restarting", "start": "Start", "starting": "Starting", "stop": "Stop", "stopping": "Stopping"}, "create-form": {"access-keys": "access keys", "add-port": "Add port", "add-tag": "Add tag", "and": "and", "azure-file-share": "Azure file share", "azure-settings": "Azure Settings", "container-configuration": "Container Configuration", "container-created": "Container successfully created", "container-resources": "Container Resources", "cpu": "CPU", "deploy-container": "Deploy the container", "deployment-in-progress": "Deployment in progress...", "enable-gpus": "Enable GPUs", "enable-volumes": "Enable volumes", "ensure-file-share-1": "Ensure you have created an", "ensure-file-share-2": "with", "ensure-file-share-3": "before adding a volume.", "failure": "Failure", "file-share-name": "File share name", "file-share-placeholder": "e.g. myshare", "gpu-count": "GPU count", "gpu-type": "GPU type", "gpus": "GPUs", "gpus-available": "During the preview, GPUs are only available in", "gpus-preview-1": "GPUs are currently a", "gpus-preview-2": "for Azure Container Instances. Make sure that you have access to GPUs to use this feature.", "image": "Image", "image-placeholder": "e.g. nginx:alpine", "linux": "Linux", "loading-locations": "Loading locations...", "loading-resource-groups": "Loading resource groups...", "loading-subnets": "Loading subnets...", "loading-subscriptions": "Loading subscriptions...", "loading-virtual-networks": "Loading virtual networks...", "location": "Location", "memory": "Memory (GB)", "mount-path": "Mount path", "mount-path-placeholder": "e.g. /mnt/share", "mount-path-tooltip": "The path within the container where the volume should be mounted", "name": "Name", "name-placeholder": "e.g. myContainer", "network-settings": "Network Settings", "no-gpus-available": "No GPUs available for the selected location.", "no-subnets": "No subnets available for the selected virtual network.", "no-subnets-available": "There are no subnets available in any available virtual network. Please try another subscription or location, or create a subnet in the Azure portal.", "no-virtual-networks": "No virtual networks available for the selected location.", "no-virtual-networks-any-location": "There are no virtual networks available in any location. Please try another subscription, or create a virtual network in the Azure portal.", "os": "OS", "port": "Port", "port-placeholder": "e.g. 80", "ports": "Ports", "preview-feature": "preview feature", "private-network": "Private Network", "public-ip-note": "This will automatically deploy an instance with a public IP address. It is highly recommended to host it in a private network.", "resource-group": "Resource Group", "select-gpu-type": "Select a GPU type", "select-subnet": "Select a subnet", "select-virtual-network": "Select a virtual network", "storage-account": "Storage account", "storage-account-key": "Storage account key", "storage-account-key-placeholder": "e.g. GbAl...RNsEtW==", "storage-account-key-tooltip": "The access key of the storage account", "storage-account-placeholder": "e.g. mystorageaccount", "storage-account-tooltip": "The name of the storage account that the file share is in", "subnet": "Subnet", "subscription": "Subscription", "tag-name": "Name", "tag-name-placeholder": "e.g. project", "tag-value": "Value", "tag-value-placeholder": "e.g. analytics", "tags": "Tags", "unable-to-create": "Unable to create container", "virtual-network": "Virtual Network", "virtual-networks-available": "There are virtual networks available in", "volumes": "Volumes", "windows": "Windows"}, "create-view": {"add-container": "Add container", "container-instances": "Container instances", "create-container-instance": "Create container instance"}, "events-datatable": {"columns": {"count": "Count", "count-tooltip": "The number of times the event has fired.", "date": "Date", "message": "Message", "type": "Type"}, "events": "Events"}, "item-view": {"actions": "Actions", "azure-settings": "Azure settings", "container": "Container", "container-configuration": "Container configuration", "container-instance-details": "Container Instance details", "container-instances": "Container instances", "cpu": "CPU", "events": "Events", "gpu": "GPU", "image": "Image", "loading-state": "Loading state", "location": "Location", "memory": "Memory", "name": "Name", "os": "OS", "published-ports": "Published ports", "resource-group": "Resource group", "state": "State", "subscription": "Subscription", "tags": "Tags", "virtual-network": "Virtual network", "volume-mounts": "Volume mounts"}, "list-view": {"add-container": "Add container", "columns": {"name": "Name", "ownership": "Ownership", "published-ports": "Published Ports", "resource-group": "Resource Group", "state": "State", "tags": "Tags", "unknown": "Unknown"}, "container-instances": "Container instances", "container-list": "Container list", "containers": "Containers"}}, "dashboard": {"breadcrumbs": "Dashboard", "container-instance": "Container Instance", "home": "Home", "resource-group": "Resource Group", "subscription": "Subscription"}}, "common": {"actions": {"save": "Save"}, "attach": "Attach", "console": "<PERSON><PERSON><PERSON>", "containers": "Containers", "description": "Description", "environments": "Environments", "failure": "Failure", "inspect": "Inspect", "logs": "Logs", "name": "Name", "nameIsRequired": "Name is required", "nameShouldBeUnique": "Name should be unique", "or": "or", "remove": "Remove", "stats": "Stats", "toggle-dropdown": "Toggle Dropdown"}, "components": {"accessControlForm": {"authorizedTeams": "Authorized teams", "authorizedUsers": "Authorized users", "noTeamsMessage": "You have not yet created any teams. Head over to the", "noUsersMessage": "You have not yet created any users. Head over to the", "teamsViewLink": "Teams view", "title": "Access control", "toManageTeams": "to manage teams.", "toManageUsers": "to manage users.", "usersViewLink": "Users view"}, "beFeatureIndicator": {"label": "Business Edition Feature"}, "informationPanel": {"dismiss": "dismiss"}, "registryDetails": {"name": "Name", "registry": "Registry", "url": "URL"}, "themeSettings": {"userTheme": "User theme"}}, "containers": {"actions": {"add-container": "Add container", "attach": "Attach", "console": "<PERSON><PERSON><PERSON>", "duplicate-edit": "Duplicate/Edit", "inspect": "Inspect", "kill": "Kill", "logs": "Logs", "pause": "Pause", "recreate": "Recreate", "recreation-in-progress": "Recreation in progress...", "remove": "Remove", "restart": "<PERSON><PERSON>", "resume": "Resume", "start": "Start", "stats": "Stats", "stop": "Stop", "title": "Actions"}, "columns": {"created": "Created", "gpus": "GPUs", "host": "Host", "image": "Image", "ipAddress": "IP Address", "name": "Name", "publishedPorts": "Published Ports", "stack": "<PERSON><PERSON>", "state": "State"}, "create-image": {"create": "Create", "title": "Create image", "will-be-used": "will be used."}, "details": {"image": "Image", "labels": "Labels", "port-configuration": "Port configuration", "restart-policies": "Restart policies", "title": "Container details"}, "list-title": "Container list", "messages": {"kill-error": "Unable to kill container", "kill-success": "Container successfully killed", "pause-error": "Unable to pause container", "pause-success": "Container successfully paused", "remove-error": "Unable to remove container", "remove-error-scheduled": "Unable to schedule container removal", "remove-success": "Container successfully removed", "remove-success-planned": "Container removal successfully planned", "restart-error": "Unable to restart container", "restart-success": "Container successfully restarted", "resume-error": "Unable to resume container", "resume-success": "Container successfully resumed", "start-error": "Unable to start container", "start-success": "Container successfully started", "stop-error": "Unable to stop container", "stop-success": "Container successfully stopped"}, "status": {"created": "Created", "edit-name": "Edit container name", "finished": "Finished", "for": "for", "id": "ID", "ip-address": "IP address", "name": "Name", "running": "running", "start-time": "Start time", "status": "Status", "title": "Container status", "with-exit-code": "with exit code"}, "volumes": {"host-volume": "Host/volume", "path-in-container": "Path in container", "title": "Volumes"}, "webhook": {"copy-link": "Copy link", "label": "Container webhook", "tooltip": "Webhook (or callback URI) used to automate the recreation of this container. Sending a POST request to this callback URI (without requiring any authentication) will pull the most up-to-date version of the associated image and recreate this container."}}, "dashboard": {"container": "Container", "environment": "Environment", "environment-info": "Environment info", "environment-summary": "Environment summary", "gpu": "GPU", "healthy": "healthy", "image": "Image", "network": "Network", "running": "running", "service": "Service", "stack": "<PERSON><PERSON>", "stopped": "stopped", "unhealthy": "unhealthy", "url": "URL", "volume": "Volume"}, "docker": {"configs": {"add-config": "Add config", "confirm-remove": "Do you want to remove the selected config(s)?", "creation-date": "Creation Date", "name": "Name", "title": "Configs"}, "container": {"actions": "Quick Actions"}, "containers": {"create": {"advanced-settings-title": "Advanced container settings", "breadcrumb": "Add container", "tabs": {"capabilities": "Capabilities", "commands": "Commands & logging", "env": "Env", "labels": "Labels", "network": "Network", "restart": "Restart policy", "runtime": "Runtime & resources", "volumes": "Volumes"}, "title": "Create container"}, "delete": {"autoRemoveVolumes": "Automatically remove non-persistent volumes", "remove": "Remove"}, "networkSelector": {"selectNetwork": "Select a network"}, "quickActions": {"attachConsole": "<PERSON><PERSON><PERSON> Console", "execConsole": "Exec Console", "inspect": "Inspect", "logs": "Logs", "stats": "Stats"}, "title": "Containers"}, "events": {"breadcrumb": "Events", "date": "Date", "details": "Details", "title": "Event list", "type": "Type"}, "imageStatus": {"reloadTooltip": "Reload image up to date indicator"}, "images": {"build": "Build a new image", "columns": {"created": "Created", "host": "Host", "id": "ID", "size": "Size", "tags": "Tags"}, "export": "Export", "export-in-progress": "Export in progress...", "force-remove": "Force Remove", "import": "Import", "item-view": {"cmd": "CMD", "dockerfile-details": "Dockerfile details", "entrypoint": "ENTRYPOINT", "env": "ENV", "expose": "EXPOSE", "volume": "VOLUME"}, "list": {"created": "Created", "host": "Host", "id": "ID", "size": "Size", "tags": "Tags"}, "registry-select": {"cancel": "Cancel", "title": "Which registry do you want to use?", "update": "Update"}, "title": "Images"}, "networks": {"actions": "Actions", "add": "Add network", "add-driver-option": "Add driver option", "add-excluded-ip": "Add excluded IP", "add-label": "Add label", "advanced-config": "Advanced configuration", "create-button": "Create the network", "creating-button": "Creating network...", "deployment": "Deployment", "driver": "Driver", "driver-configuration": "Driver configuration", "driver-option-name": "name", "driver-option-name-placeholder": "e.g. com.docker.network.bridge.enable_icc", "driver-option-value": "value", "driver-option-value-placeholder": "e.g. true", "driver-options": "Driver options", "driver-placeholder": "e.g. <PERSON><PERSON><PERSON>", "enable-manual-attachment": "Enable manual container attachment", "exclude-ip": "Exclude IP", "exclude-ip-error": "Exclude ip cannot be the same as gateway.", "exclude-ip-placeholder": "e.g. my-router=2001:db8::1", "gateway": "Gateway", "ip-range": "IP range", "ipv4-config": "IPV4 Network configuration", "ipv4-gateway-placeholder": "e.g. ************", "ipv4-iprange-placeholder": "e.g. *************/25", "ipv4-subnet-placeholder": "e.g. **********/16", "ipv6-config": "IPV6 Network configuration", "ipv6-gateway-placeholder": "e.g. 2001:db8::1", "ipv6-iprange-placeholder": "e.g. 2001:db8::/64", "ipv6-subnet-placeholder": "e.g. 2001:db8::/48", "isolated-network": "Isolated network", "isolated-network-tooltip": "An isolated network has no inbound or outbound communications.", "label-name": "name", "label-name-placeholder": "e.g. com.example.foo", "label-value": "value", "label-value-placeholder": "e.g. bar", "name-placeholder": "e.g. myNetwork", "network-details-title": "Network details", "notifications": {"network-removed-success": "Network successfully removed"}, "remove-confirm": "Do you want to remove the selected network(s)?", "select-driver": "Select a driver", "subnet": "Subnet", "table-label": "Networks table", "title": "Networks"}, "services": {"apply-changes": "Apply changes", "create": {"actions": "Actions", "addAdditionalEntry": "add additional entry", "addContainerLabel": "add container label", "addExtraNetwork": "add extra network", "addLoggingDriverOption": "add logging driver option", "addServiceLabel": "add service label", "bind": "Bind", "command": "Command", "commandAndLogging": "Command & Logging", "commandPlaceholder": "e.g. /usr/bin/nginx -t -c /mynginx.conf", "configs": "Configs", "container": "container", "containerLabelNamePlaceholder": "e.g. com.example.foo", "containerLabelValuePlaceholder": "e.g. bar", "containerLabels": "Container labels", "containerPathPlaceholder": "e.g. /path/in/container", "containerPortPlaceholder": "e.g. 80", "createService": "Create the service", "createServiceWebhook": "Create a service webhook", "creatingService": "Creating service...", "defaultLoggingDriver": "Default logging driver", "driver": "Driver", "entrypoint": "Entrypoint", "entrypointPlaceholder": "e.g. /bin/sh -c", "env": "Env", "extraNetworks": "Extra networks", "global": "Global", "host": "host", "hostMode": "Host", "hostPathPlaceholder": "e.g. /path/on/host", "hostPortPlaceholder": "e.g. 80 or *******:80 (optional)", "hostsEntryPlaceholder": "e.g. host:IP", "hostsFileEntries": "Hosts file entries", "imageConfiguration": "Image configuration", "inDockerDocumentation": "in the Docker documentation", "ingress": "Ingress", "labelNamePlaceholder": "e.g. com.example.foo", "labelValuePlaceholder": "e.g. bar", "labels": "Labels", "logging": "Logging", "mapAdditionalPort": "map additional port", "mapAdditionalVolume": "map additional volume", "name": "name", "namePlaceholder": "e.g. myService", "network": "Network", "none": "none", "option": "option", "optionPlaceholder": "e.g. FOO", "options": "Options", "optionsTooltip": "Add button is disabled unless a driver other than none or default is selected. Options are specific to the selected driver, refer to the driver documentation.", "portMapping": "Port mapping", "portsConfiguration": "Ports configuration", "readOnly": "Read-only", "replicas": "Replicas", "replicasPlaceholder": "e.g. 3", "replicated": "Replicated", "resourcesAndPlacement": "Resources & Placement", "scheduling": "Scheduling", "schedulingMode": "Scheduling mode", "secrets": "Secrets", "selectNetwork": "Select a network", "selectVolume": "Select a volume", "serviceLabels": "Service labels", "sourceRequired": "Source is required.", "targetRequired": "Target is required.", "tcp": "TCP", "udp": "UDP", "updateConfigAndRestart": "Update config & Restart", "user": "User", "userPlaceholder": "e.g. nginx", "value": "value", "valuePlaceholder": "e.g. bar", "volume": "volume", "volumeMapping": "Volume mapping", "volumes": "Volumes", "webhookTooltip": "Create a webhook (or callback URI) to automate the update of this service. Sending a POST request to this callback URI (without requiring any authentication) will pull the most up-to-date version of the associated image and re-deploy this service.", "webhooks": "Webhooks", "workingDir": "Working Dir", "workingDirPlaceholder": "e.g. /myapp", "writable": "Writable"}, "image": "Image", "published-ports": "Published Ports", "reset-all-changes": "Reset all changes", "reset-changes": "Reset changes", "title": "Services"}, "volumes": {"add-driver-option": "add driver option", "browse": "Browse", "created": "Created", "driver": "Driver", "driver-configuration": "Driver configuration", "driver-options": "Driver options", "driver-placeholder": "e.g. <PERSON><PERSON><PERSON>", "filter": {"unused": "Unused", "used": "Used"}, "filter-by-usage": "Filter by usage", "host": "Host", "mount-point": "Mount point", "name-placeholder": "e.g. myVolume", "select-driver": "Select a driver", "stack": "<PERSON><PERSON>", "title": "Volumes"}}, "edge": {"associatedEnvironments": "Associated environments", "availableEnvironments": "Available environments", "environmentsSelectorDescription": "You can also select environments individually by moving them to the associated environments table. Simply click on any environment entry to move it from one table to the other.", "group": "Group", "name": "Name", "pollFrequency": "Poll frequency", "pollFrequencyTooltip": "Interval used by this Edge agent to check in with the Portainer instance. Affects Edge environment management and Edge compute features.", "tags": "Tags", "useDefaultInterval": "Use default interval"}, "endpoints": {"agent-communication-message": "The agent will communicate with <PERSON><PERSON><PERSON> via", "amt-version-label": "AMT Version", "and-text": "and", "build-number-label": "Build Number", "cancel-button": "Cancel", "check-in-intervals-title": "Check-in Intervals", "configuration-title": "Configuration", "control-mode-label": "Control Mode", "deploy-agent-message": "Refer to the platform related command below to deploy the Edge agent in your remote cluster.", "deploy-agent-title": "Deploy an agent", "deployment-script-title": "Edge agent deployment script", "details-title": "Environment details", "disassociate-button": "Disassociate", "dns-suffix-label": "DNS Suffix", "edge-agent-https-message": "Use https connection on Edge agent to use private registries with credentials.", "edge-associated-message": "This Edge environment is associated to an Edge environment", "edge-identifier-label": "Edge identifier", "edge-information-title": "Edge information", "edge-key-label": "Edge key", "environment-address-label": "Environment address", "environment-url-label": "Environment URL", "environment-url-placeholder": "e.g. *********:2375 or mydocker.mydomain.com:2375", "go-to-portal-button": "Go to portal", "group-label": "Group", "kaas-cluster-details-title": "KaaS cluster details", "kubernetes-config-link": "Kubernetes configuration", "kubernetes-config-message": "You should configure the features available in this Kubernetes environment in the", "kubernetes-config-title": "Kubernetes features configuration", "loading-placeholder": "Loading...", "metadata-title": "<PERSON><PERSON><PERSON>", "name-label": "Name", "name-placeholder": "e.g. kubernetes-cluster01 / docker-prod01", "network-id-label": "Network Id", "node-ips-label": "Node IPs", "node-size-label": "Node Size", "open-amt-title": "Open Active Management Technology", "provider-label": "Provider", "public-ip-label": "Public IP", "public-ip-placeholder": "e.g. ********* or mydocker.mydomain.com", "region-label": "Region", "update-environment-button": "Update environment...", "updating-environment-button": "Updating environment...", "uuid-label": "UUID", "view-text": "view."}, "environments": {"actions": "Actions", "dismiss-error": "Dismiss error (still visible in logs)", "error-dismissed": "Error dismissed successfully", "group-name": "Group Name", "manage-access": "Manage access", "name": "Name", "type": "Type", "url": "URL"}, "for more information": {"": "for more information."}, "gitops": {"add_file": "Add file", "additional_paths": "Additional paths", "auth": {"basic": "Basic", "token": "Token"}, "auth_explanation": "Enabling authentication will store the credentials and it is advisable to use a git service account", "authentication": "Authentication", "authorization_type": "Authorization type", "change_window_enabled": "A change window is enabled, GitOps updates will not occur outside of", "compose_file": "Compose", "compose_path": "Compose path", "copy_link": "Copy link", "credential_management_tip": "This git credential can be managed through your account page", "credential_name_placeholder": "credential name", "current_version": "and the current version deployed is", "device_matching_rule": "Device matching rule", "directory": "Directory", "docker_documentation": "Docker documentation", "edge_config_files_tip": "Files named", "edge_config_files_tip_and_or": "and/or", "edge_config_files_tip_contained": "contained by the config folder will be loaded for compose file interpolation.", "edge_id_tip": "When enabled, corresponding Edge ID will be passed through as an environment variable: PORTAINER_EDGE_ID.", "enable_relative_path_volumes": "Enable relative path volumes", "enable_relative_path_volumes_tooltip": "Enabling this means you can specify relative path volumes in your Compose files, with <PERSON><PERSON><PERSON> pulling the content from your git repository to the environment the stack is deployed to.", "environment_variable_tip": "You can use it as an environment variable with an image:", "environment_variable_tip_docs": "More documentation can be found", "environment_variable_tip_here": "here", "environment_variable_tip_or": "or", "environment_variable_tip_volumes": "You can also use it with the relative path for volumes:", "environment_variable_tip_volumes_or": "or", "fetch_interval": "Fetch interval", "fetch_interval_tooltip": "Specify how frequently polling occurs using syntax such as, 5m = 5 minutes, 24h = 24 hours, 6h40m = 6 hours and 40 minutes.", "force_redeployment": "Force redeployment", "from_root": "from the root of your repository (requires a yaml, yml, json, or hcl file extension).", "git_credentials": "Git Credentials", "git_repository": "Git repository", "gitops_edge_configurations": "GitOps Edge configurations", "gitops_edge_configurations_header": "GitOps Edge Configurations", "gitops_edge_configurations_tooltip": "By enabling the GitOps Edge Configurations feature, you gain the ability to define relative path volumes in your configuration files. Portainer will then automatically fetch the content from your git repository by matching the folder name or file name with the Portainer Edge ID, and apply it to the environment where the stack is deployed", "gitops_updates": "GitOps updates", "gitops_updates_tooltip": "When enabled, at each polling interval or webhook invocation, if the git repo differs from what was stored locally on the last git pull, the changes are deployed.", "group_matching_rule": "Group matching rule", "indicate_path": "Indicate the path to the", "kubernetes_manifest_file": "Kubernetes manifest file", "local_filesystem_path": "Local filesystem path", "manifest_path": "Manifest path", "match_file_name_edge_group": "Match file name with Edge Group", "match_file_name_edge_id": "Match file name with Portainer Edge ID", "match_folder_name_edge_group": "Match folder name with Edge Group", "match_folder_name_edge_id": "Match folder name with Portainer Edge ID", "no_saved_credentials": "no saved credentials", "path": "path", "personal_access_token": "Personal Access Token", "ref_field_tip_be": "Specify a reference of the repository using the following syntax: branches with", "ref_field_tip_be_or": "or tags with", "ref_field_tip_branch": "reference normally the", "ref_field_tip_ce": "Specify a reference of the repository using the following syntax: branches with", "ref_field_tip_ce_or": "or tags with", "ref_field_tip_default": "If not specified, will use the default", "refresh_git_repo": "refreshGitRepo", "refs_heads_main": "refs/heads/main", "relative_path_tip_swarm": "For relative path volumes use with Docker Swarm, you must have a network filesystem which all of your nodes can access.", "repository_reference": "Repository reference", "repository_url": "Repository URL", "repository_url_placeholder": "https://github.com/portainer/portainer-compose", "save_credential": "save credential", "select_git_credential": "select git credential or fill in below", "select_rule_tip": "Select which rule to use when matching configuration with Portainer Edge ID either on a per-device basis or group-wide with an Edge Group. Only configurations that match the selected rule will be accessible through their corresponding paths. Deployments that rely on accessing the configuration may experience errors.", "skip_tls_verification": "Skip TLS Verification", "skip_tls_verification_tooltip": "Enabling this will allow skipping TLS validation for any self-signed certificate.", "specify_directory_tip": "Specify the directory name where your configuration will be located. This will allow you to manage device configuration settings with a Git repo as your template.", "token_tooltip": "Provide a personal access token or password", "update_files": "Update", "username": "Username", "username_placeholder": "git username", "webhook": "Webhook", "webhook_documentation": "Portainer documentation on webhook usage"}, "home": {"administrator": "administrator.", "confirmSnapshotMessage": "Triggering a manual refresh will poll each environment to retrieve its information, this may take a few moments.", "confirmSnapshotTitle": "Are you sure?", "environment": {"group": "Group:", "last_snapshot_time": "Last snapshot time", "loading_tags": "Loading tags...", "no_tags": "No tags"}, "environmentWizardLink": "environment wizard", "environmentsBreadcrumb": "Environments", "environmentsDescription": "Click on an environment to manage", "environmentsTitle": "Environments", "environmentsUpdated": "Environments updated", "failedConnectionTitle": "Failed connecting to ${params.environmentName}", "failureTitle": "Failure", "informationTitle": "Information", "loading": "Loading...", "noEnvironments": "No environments available.", "noEnvironmentsAdminMessage": "No environment available for management. Please head over the", "noEnvironmentsUserMessage": "You do not have access to any environment. Please contact your", "refreshButton": "Refresh", "retryButton": "Retry", "scheduleUpdate": "Schedule Update", "searchPlaceholder": "Search by name, group, tag, status, URL...", "snapshotError": "An error occurred during environment snapshot", "successTitle": "Success", "title": "Home", "toAddEnvironment": "to add an environment."}, "images": {"import": {"actions-title": "Actions", "deployment-title": "Deployment", "select-file": "Select file", "tag-image-title": "Tag the image", "upload-button": "Upload", "upload-description": "You can upload a tar archive containing your images.", "upload-in-progress": "Images uploading in progress...", "upload-title": "Upload"}}, "in the agent deployment": {" Check": "in the agent deployment. Check"}, "internalAuth": {"authenticationInProgress": "Authentication in progress...", "loginButton": "<PERSON><PERSON>", "loginInProgress": "Login in progress...", "loginTitle": "Log in to your account", "passwordLabel": "Password", "passwordPlaceholder": "Enter your password", "usernameLabel": "Username", "usernamePlaceholder": "Enter your username", "welcomeMessage": "Welcome back! Please enter your details"}, "kubernetes": {"applications": {"add_with_form": "Add with form", "confirm_remove": "Do you want to remove the selected application(s)?", "deploy": "Deploy application", "deployment_in_progress": "Deployment in progress...", "title": "Applications", "update": "Update application", "update_in_progress": "Update in progress..."}, "cluster": {"information": "Cluster information", "nodes": "Nodes", "resource-reservation-description": "Resource reservation represents the total amount of resource assigned to all the applications inside the cluster.", "title": "Cluster"}, "deploy": {"from": "Deploy from", "namespace": "Namespace", "title": "Deploy Kubernetes resources", "to": "Deploy to"}, "namespaces": {"add-with-form": "Add with form", "manage-access": "Manage access", "remove-confirm": "Do you want to remove the selected ${selectedNamespacePlural}? All the resources associated to the selected ${selectedNamespacePlural} will be removed too. Are you sure you wish to proceed?", "terminating-warning": "At least one namespace is in a terminating state. For terminating state namespaces, you may continue and force removal, but doing so without having properly cleaned up may lead to unstable and unpredictable behavior. Are you sure you wish to proceed?", "title": "Namespaces"}, "summary": {"cpu_limit": "Set the CPU resources limits and requests to", "description": "<PERSON><PERSON><PERSON> will execute the following Kubernetes actions.", "memory_limit": "Set the memory resources limits and requests to", "title": "Summary"}, "volumes": {"confirm_remove": "Do you want to remove the selected volume(s)?", "title": "Volumes"}}, "logout": {"inProgress": "Logout in progress..."}, "navigation": {"containers": "Containers", "dashboard": "Dashboard", "home": "Home"}, "oauth": {"adminMapping": {"addMapping": "add admin mapping", "assignRights": "Assign admin rights to group(s)", "claimRequired": "Claim value regex is required.", "claimValueRegex": "claim value regex", "title": "Admin mapping"}, "autoProvision": {"defaultTeam": "Default team", "defaultTeamInfo1": "The users created by the automatic provisioning feature can be added to a default team on creation.", "description": "With automatic user provisioning enabled, <PERSON><PERSON><PERSON> will create user(s) automatically with the standard user role. If disabled, users must be created beforehand in Portainer in order to login.", "disabledWhenAuto": "The default team option will be disabled when automatic team membership is enabled", "noTeam": "No team"}, "configuration": {"accessTokenUrl": "Access token URL", "authorizationUrl": "Authorization URL", "logoutUrl": "Logout URL", "override": "Override default configuration", "redirectUrl": "Redirect URL", "resourceUrl": "Resource URL", "scopes": "<PERSON><PERSON><PERSON>", "tenantId": "Tenant ID", "title": "OAuth Configuration", "useDefault": "Use default configuration", "userIdentifier": "User identifier"}, "provider": "Provider", "sso": {"title": "Single Sign-On"}, "teamMembership": {"addMapping": "add team mapping", "claimName": "Claim name", "claimNamePlaceholder": "groups", "claimRequired": "Claim value regex is required.", "claimValueRegex": "claim value regex", "defaultTeam": "Default team", "defaultTeamInfo": "The default team will be assigned when the user does not belong to any other team", "description": "Automatic team membership synchronizes the team membership based on a custom claim in the token from the OAuth provider.", "mapsTo": "maps to", "noTeam": "No team", "selectTeam": "Select a team", "staticTeams": "Statically assigned teams", "team": "team", "title": "Team membership"}}, "our documentation": "our documentation", "portainer": {"endpoints": {"access": {"breadcrumbs": "Access management", "environment_title": "Environment", "group_label": "Group", "name_label": "Name", "title": "Environment access", "url_label": "URL"}, "kvm": {"breadcrumbs": "KVM Control", "maximize_button": "Maximize", "minimize_button": "Minimize", "title": "KVM Control", "toolbar_title": "KVM Control"}}, "environments": {"groups": {"datatable": {"title": "Environment Groups"}}, "update": {"datatable": {"title": "Update & rollback"}}}, "groups": {"access": {"breadcrumbs": "Access management", "group_title": "Group", "name_label": "Name", "title": "Environment group access"}, "create": {"breadcrumbs": "Add group", "form_action_label": "Create the group", "title": "Create environment group"}, "edit": {"form_action_label": "Update the group", "title": "Environment group details"}}, "init": {"admin": {"access_key_label": "Access key ID", "admin_note": "Please create the initial administrator user.", "bucket_name_label": "Bucket name", "confirm_password_label": "Confirm password", "create_user_button": "Create user", "creating_user_button": "Creating user...", "filename_label": "Filename", "install_title": "New Portainer installation", "password_label": "Password", "password_length_note": "The password must be at least", "password_length_note2": "characters long.", "password_tooltip": "If the backup is password protected, provide the password in order to extract the backup file, otherwise this field can be left empty.", "privacy_policy_link": "privacy policy", "region_label": "Region", "region_placeholder": "default region is us-east-1 if left empty", "restore_button": "<PERSON><PERSON>", "restore_note": "This will restore the Portainer metadata which contains information about the environments, stacks and applications, as well as the configured users.", "restore_note2": "You are about to restore <PERSON><PERSON><PERSON> from this backup.", "restore_note3": "After restoring has completed, please log in as a user that was known by the Portainer that was restored.", "restore_title": "<PERSON><PERSON> from backup", "restoring_button": "Restoring Portainer...", "s3_host_label": "S3 Compatible Host", "s3_host_placeholder": "leave empty for AWS S3", "s3_host_tooltip": "Hostname of a S3 service", "secret_access_key_label": "Secret access key", "select_file_button": "Select file", "telemetry_note": "Allow collection of anonymous statistics. You can find more information about this in our", "upload_note": "You can upload a backup file from your computer.", "username_label": "Username"}, "license": {"in_progress_button": "In progress...", "key_note": "Your license key should start with “2-” or “3-”.", "license_label": "License", "register_note": "Please register your Portainer license.", "submit_button": "Submit", "title": "License registration"}}, "licenses": {"datatable": {"actions_header": "Actions", "add_button": "Add license", "company_header": "Company", "confirm_remove": "Are you sure you want to remove these licenses?", "expires_header": "Expires", "nodes_header": "Nodes", "title": "Licenses", "type_header": "Type", "valid_header": "<PERSON><PERSON>"}}, "registries": {"datatable": {"title": "Registries"}}, "settings": {"authentication": {"auth_method_title": "Authentication method", "auth_title": "Authentication", "breadcrumbs": "Authentication", "config_title": "Configuration", "session_lifetime_label": "Session lifetime", "session_lifetime_note": "Changing from default is only recommended if you have additional layers of authentication in front of <PERSON><PERSON><PERSON>.", "session_lifetime_tooltip": "Time before users are forced to relogin.", "title": "Authentication settings"}, "edge": {"breadcrumbs": "Edge Compute", "title": "Settings"}}, "stacks": {"breadcrumbs": "Stacks", "edit": {"actions_title": "Actions", "associate_button": "Associate", "associate_note": "This feature allows you to re-associate this stack to the current environment.", "associate_title": "Associate to this environment", "associating_button": "Association in progress...", "compose_docs_link": "official documentation", "compose_docs_note": "You can get more information about Compose file format in the", "compose_v2_note": "This stack will be deployed using the equivalent of <code>docker compose</code>. Only Compose file format\n                  version <b>2</b> is supported at the moment.", "compose_v3_note": "This stack will be deployed using <code>docker compose</code>.", "copy_link_button": "Copy link", "create_template_button": "Create template from stack", "delete_button": "Delete this stack", "detach_button": "Detach from Git", "detaching_button": "Detachment in progress...", "details_title": "Stack details", "editor_tab": "Editor", "editor_tip": "Define or paste the content of your docker compose file here", "external_note": "This stack was created outside of Portainer. Control over this stack is limited.", "info_title": "Information", "loading_registries": "Loading registries...", "no_registries_text": "No registries available. Please configure registries first.", "options_title": "Options", "prune_label": "Prune services", "prune_tooltip": "Prune services that are no longer referenced.", "registries_title": "Registrie(s)", "select_registries_label": "Select Registries", "select_registries_placeholder": "Choose one or more registries for this stack", "stack_tab": "<PERSON><PERSON>", "start_button": "Start this stack", "stop_button": "Stop this stack", "title": "Stack details", "update_button": "Update the stack", "updating_button": "Deployment in progress...", "webhook_label": "Webhook", "webhook_tooltip": "Create a webhook (or callback URI) to automate the update of this stack. Sending a POST request to this callback URI (without requiring any authentication) will pull the most up-to-date version of the associated image and re-deploy this stack.", "webhooks_title": "Webhooks"}, "title": "Stacks list"}, "teams": {"datatable": {"confirm_remove": "Are you sure you want to remove the selected teams?", "failure": "Failure", "remove_error": "Unable to remove team", "remove_success": "Teams successfully removed", "title": "Teams"}}}, "rbac": {"accessViewer": {"noUser": "No user available", "title": "Effective access viewer", "user": "User"}, "default": "<PERSON><PERSON><PERSON>", "description": "Description", "name": "Name", "roles": "Roles", "scope": "<PERSON><PERSON>"}, "registries": {"create": {"addRegistry": "Add registry", "provider": "Registry provider"}, "edit": {"authentication": "Authentication", "baseUrl": "Base URL", "baseUrlPlaceholder": "e.g. *********:5000 or myregistry.domain.tld", "baseUrlRequired": "This field is required.", "cancel": "Cancel", "name": "Name", "nameExists": "A registry with the same name already exists.", "namePlaceholder": "e.g. my-registry", "nameRequired": "This field is required.", "orgName": "Organization name", "orgNameRequired": "This field is required.", "passwordRequired": "This field is required.", "provider": "Provider", "region": "Region", "regionPlaceholder": "us-west-1", "regionRequired": "This field is required.", "registryUrl": "Registry URL", "registryUrlPlaceholder": "e.g. *********:5000 or myregistry.domain.tld", "registryUrlRequired": "This field is required.", "updateRegistry": "Update registry", "updatingRegistry": "Updating registry...", "useOrgRegistry": "Use organization registry", "usernameRequired": "This field is required."}}, "running the Portainer Agent": "running the Portainer Agent", "settings": {"application_settings": {"edge_agent_checkin_interval": "Edge agent default poll frequency", "save": "Save application settings", "saving": "Saving...", "snapshot_interval": "Snapshot interval", "snapshot_interval_placeholder": "e.g. 15m", "title": "Application settings", "updated": "Application settings updated"}, "breadcrumbs": "Settings", "support": {"bundle": "Support bundle", "bundle_info": "This will collect a bundle of files from Portainer to help business support troubleshoot any issues.", "debug_log": "Debug log", "debug_tooltip": "Set the server log level to debug to help troubleshoot issues.", "debug_updated": "Successfully updated debug log settings", "download_bundle": "Download support bundle", "download_success": "Downloaded support bundle successfully", "enable_debug_log": "Enable debug log", "loading": "Loading...", "preparing_bundle": "Preparing bundle...", "save_debug_settings": "Save debug settings", "saving_settings": "Saving settings...", "title": "Portainer support"}, "title": "Settings"}, "sidebar": {"activity": "Activity", "administration": "Administration", "application": "Application", "authentication": "Authentication", "browsingSnapshot": "Browsing snapshot", "clearEnvironment": "Clear environment", "custom": "Custom", "edgeCompute": "Edge Compute", "edgeConfigurations": "Edge Configurations", "edgeGroups": "Edge Groups", "edgeJobs": "Edge Jobs", "edgeStacks": "<PERSON> Stacks", "edgeTemplates": "Edge Templates", "environment": "Environment:", "environmentRelated": "Environment-related", "environments": "Environments", "general": "General", "getHelp": "Get Help", "groups": "Groups", "licenses": "Licenses", "logo": "Logo", "logs": "Logs", "noneSelected": "None selected", "notifications": "Notifications", "poweredBy": "Powered by", "registries": "Registries", "roles": "Roles", "settings": "Settings", "sharedCredentials": "Shared Credentials", "tags": "Tags", "teams": "Teams", "toggleSidebar": "Toggle Sidebar", "updateAndRollback": "Update & Rollback", "userRelated": "User-related", "users": "Users", "waitingRoom": "Waiting Room"}, "stacks": {"create": {"actions_title": "Actions", "build_method_title": "Build method", "compose_deployment_info": "This stack will be deployed using <code>docker compose</code>.", "compose_docs_link": "You can get more information about Compose file format in the", "compose_docs_link_text": "official documentation", "compose_v2_deployment_info": "This stack will be deployed using the equivalent of <code>docker compose</code>. Only Compose file format\n                version <b>2</b> is supported at the moment.", "deploy_button": "Deploy the stack", "deploy_in_progress": "Deployment in progress...", "editor_tip": "Define or paste the content of your docker compose file here", "libcompose_limitation_note": "Note: Due to a limitation of libcompose, the name of the stack will be standardized to remove all special characters and uppercase letters.", "loading_registries": "Loading registries...", "local_filesystem_path_label": "Local filesystem path", "local_filesystem_path_required": "Local filesystem path is required", "name_label": "Name", "name_placeholder": "e.g. mystack", "network_filesystem_path_label": "Network filesystem path", "network_filesystem_path_required": "Network filesystem path is required", "no_registries_available": "No registries available. Please configure registries first.", "registries_title": "Registrie(s)", "relative_paths_label": "Enable relative path volumes", "relative_paths_tooltip": "Enabling this means you can specify relative path volumes in your Compose files, with <PERSON><PERSON><PERSON> pulling the content from your git repository to the environment the stack is deployed to.", "select_file_button": "Select file", "select_registries_label": "Select Registries", "select_registries_placeholder": "Choose one or more registries for this stack", "swarm_deployment_info": "This stack will be deployed using the equivalent of the <code>docker stack deploy</code> command.", "swarm_relative_paths_info": "For relative path volumes use with Docker Swarm, you must have a network filesystem which all of your nodes can access.", "template_load_error_admin": "Custom template could not be loaded, please", "template_load_error_admin_config": "for configuration.", "template_load_error_admin_link": "click here", "template_load_error_user": "Custom template could not be loaded, please contact your administrator.", "upload_description": "You can upload a Compose file from your computer.", "upload_title": "Upload", "webhook_label": "Create a Stack webhook", "webhook_tooltip": "Create a webhook (or callback URI) to automate the update of this stack. Sending a POST request to this callback URI (without requiring any authentication) will pull the most up-to-date version of the associated image and re-deploy this stack.", "webhooks_title": "Webhooks"}, "dockerComposeDescription": "You can get more information about Compose file format in the", "dockerComposePlaceholder": "Define or paste the content of your docker compose file here", "kubernetesFileFormatDescription": "You can get more information about Kubernetes file format in the", "kubernetesManifestPlaceholder": "Define or paste the content of your manifest file here", "kubernetesTemplatesDescription": "Templates allow deploying any kind of Kubernetes resource (Deployment, Secret, ConfigMap...)", "list": {"created_header": "Created", "filter_by_activity": "Filter by activity", "inactive_label": "Inactive", "name_column": "Name", "title": "Stacks", "type_header": "Type", "updated_header": "Updated"}, "officialDocumentation": "official documentation", "uploadComposeFile": "You can upload a Compose file from your computer.", "uploadManifestFile": "You can upload a Manifest file from your computer."}, "success": {"title": "Success"}, "tags": {"addNewTag": "Add a new tag", "createTag": "Create tag", "creatingTag": "Creating tag...", "fieldRequired": "This field is required.", "name": "Name", "noTagsAvailable": "No tags available", "noTagsAvailableMessage": "No tags available. Head over to the", "removeTagTitle": "Remove tag", "removeTagsConfirmation": "Are you sure you want to remove the selected tag(s)?", "selectedTags": "Selected tags", "tagAlreadyExists": "This tag already exists.", "tags": "Tags", "tagsViewLink": "Tags view", "toAddTags": "to add tags"}, "to use this functionality, and the root of the host must be bind-mounted to": "to use this functionality, and the root of the host must be bind-mounted to", "userActivity": {"authLogs": {"dateRange": "Date range", "exportCSV": "Export as CSV", "retentionInfo": "Portainer user authentication logs have a maximum retention of 7 days."}}, "users": {"add-new-user": "Add a new user", "authentication": "Authentication", "change-password": "Change user password", "create-success": "User successfully created", "create-user": "Create user", "creating-user": "Creating user...", "delete-user": "Delete this user", "ldap-username-tooltip": "Username must exactly match username defined in external LDAP source.", "name": "Name", "remove-confirm": "Do you want to remove the selected users? They will not be able to login into Portainer anymore.", "role": "Role", "title": "Users", "user-details": "User details", "username-placeholder": "e.g. jdoe"}}